### get device code using github app: "GitHub Copilot for vscode"
# get $.user_code code and save $.device_code
# and notify user navigate to $.verification_uri in $.expires_in seconds
# if time runs out in $.expires_in seconds, notify user to try again
POST https://github.com/login/device/code
Content-Type: application/json
Accept: application/json

{
  "client_id": "Iv1.b507a08c87ecfe98",
  "scope": "read:user"
}

### get access token
# device_code be the first steop saved
# get $.access_token and save it
POST https://github.com/login/oauth/access_token
Content-Type: application/json
Accept: application/json

{
  "client_id": "Iv1.b507a08c87ecfe98",
  "device_code": "72976c009238f0d23b214115fc62208f605e452b",
  "grant_type": "urn:ietf:params:oauth:grant-type:device_code"
}

### get api token
# use access_token saved last step in Authorization
# get $.token and save it
GET https://api.github.com/copilot_internal/v2/token
Authorization: token ****************************************
Accept: application/json
User-Agent: Zed/1.89.3


### Send POST to github copilot
# use token saved at "get api token" step in Authorization
# response sample: .idea/httpRequests/2025-05-28T210845.200.json
GET https://api.githubcopilot.com/models
Authorization: Bearer tid=e48f06bddae42040bec21d3689e6efb0;exp=1748438788;sku=free_engaged_oss;proxy-ep=proxy.individual.githubcopilot.com;st=dotcom;chat=1;cit=1;malfil=1;editor_preview_features=1;ccr=1;8kp=1;ip=**************;asn=AS63949:284c49a6d709ab09136093f1d9da4e63ef8c70bf0bf09def380904513800cab3
Accept: application/json
Editor-Version: vscode-chat
Copilot-Integration-Id: vscode-chat

### Send POST to github copilot
# use token saved at "get api token" step in Authorization
# response sample: .idea/httpRequests/2025-05-28T210845.200.json
POST https://api.githubcopilot.com/chat/completions
Authorization: Bearer tid=e48f06bddae42040bec21d3689e6efb0;exp=1748438788;sku=free_engaged_oss;proxy-ep=proxy.individual.githubcopilot.com;st=dotcom;chat=1;cit=1;malfil=1;editor_preview_features=1;ccr=1;8kp=1;ip=**************;asn=AS63949:284c49a6d709ab09136093f1d9da4e63ef8c70bf0bf09def380904513800cab3
Editor-Version: vscode-chat
Content-Type: application/json
Copilot-Integration-Id: vscode-chat
User-Agent: Githubcopilot/1.189.0

{
  "messages": [
    {
      "role": "user",
      "content": "帮我写个kotlin的hello world"
    }
  ],
  "intent": true,
  "model": "claude-3.7-sonnet-thought",
  "stream": true
}

